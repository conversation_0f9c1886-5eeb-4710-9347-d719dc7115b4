import * as THREE from "three";
import { EARTH_FRAGMENTS, EARTH_RADIUS, PATHS } from "../../../../constants";
import { texturePreloader } from "../../utils/TexturePreloader.js";
import { G<PERSON> } from "lil-gui";

class Clouds {
  constructor(scene, { isActive = true, renderer } = {}) {
    this.scene = scene;
    this.isActive = isActive;
    this.renderer = renderer;
    this.mesh = null;
    this.geometry = null;
    this.material = null;
    this.texture = null;

    // Animation properties
    this.animationSpeed = {
      x: -0.0, // 增加X轴旋转速度
      y: -0.0015, // 增加Y轴旋转速度，模拟风向
      z: -0.0, // 添加Z轴旋转，增加动态效果
    };
    this.time = 0; // 用于创建更复杂的动画

    // Animation control flags
    this.animationFlags = {
      rotation: true, // 控制旋转动画
      pulse: false, // 控制脉动效果
      opacity: false, // 控制透明度动画
    };

    // Material properties for GUI control
    this.materialProperties = {
      opacity: 0.07,
      color: "#ffffff",
      alphaTest: 0,
      visible: true,
      // MeshPhysicalMaterial specific properties
      metalness: 0.0,
      roughness: 1.0,
      clearcoat: 0.0,
      clearcoatRoughness: 0.0,
      transmission: 0.0,
      thickness: 0.0,
      ior: 1.5,
      reflectivity: 0.5,
      iridescence: 0.0,
      iridescenceIOR: 1.3,
      sheen: 0.0,
      sheenRoughness: 1.0,
      sheenColor: "#ffffff",
      specularIntensity: 1.0,
      specularColor: "#ffffff",
    };

    // Scale properties for GUI control
    this.scaleProperties = {
      x: 1.03,
      y: 1.03,
      z: 1.03,
      uniform: 1.03, // 统一缩放控制
    };

    // GUI properties
    this.gui = null;
    this.folder = null;

    this.init();
  }

  async init() {
    await this.loadTexture();
    this.createGeometry();
    this.createMaterial();
    this.createMesh();
    this.addToScene();
    // this.initGUI();
  }

  async loadTexture() {
    try {
      // 优先使用预加载的纹理
      const preloadedTextures = texturePreloader.getAllTextures();

      if (preloadedTextures.clouds) {
        console.log("Clouds: 使用预加载的纹理");
        this.texture = preloadedTextures.clouds;
      } else {
        console.log("Clouds: 预加载纹理不可用，使用异步加载");

        // 如果预加载纹理不可用，使用传统的异步加载方式
        const textureLoader = new THREE.TextureLoader();
        this.texture = await this.loadTexturePromise(textureLoader, PATHS.clouds);
      }

      // Set anisotropy for better quality
      const maxAnisotropy = this.renderer?.capabilities?.getMaxAnisotropy?.() || 16;
      this.texture.anisotropy = maxAnisotropy;

      console.log("Clouds: 纹理加载完成");
    } catch (error) {
      console.error("Error loading Clouds texture:", error);
    }
  }

  loadTexturePromise(loader, url) {
    return new Promise((resolve, reject) => {
      loader.load(
        url,
        (texture) => resolve(texture),
        undefined,
        (error) => reject(error)
      );
    });
  }

  createGeometry() {
    this.geometry = new THREE.SphereGeometry(EARTH_RADIUS, EARTH_FRAGMENTS, EARTH_FRAGMENTS);
  }

  createMaterial() {
    this.material = new THREE.MeshPhysicalMaterial({
      map: this.texture,
      alphaMap: this.texture, // 恢复alphaMap以正确处理纹理透明度
      transparent: true,
      opacity: this.materialProperties.opacity,
      color: this.materialProperties.color,
      // 增强云层的视觉效果
      alphaTest: this.materialProperties.alphaTest, // 提高透明度测试阈值，让云层边缘更清晰
      side: THREE.DoubleSide, // 双面渲染，确保从各个角度都能看到云层
      depthWrite: false, // 禁用深度写入，避免透明度问题
      blending: THREE.NormalBlending, // 使用正常混合模式

      // MeshPhysicalMaterial specific properties
      metalness: this.materialProperties.metalness,
      roughness: this.materialProperties.roughness,
      clearcoat: this.materialProperties.clearcoat,
      clearcoatRoughness: this.materialProperties.clearcoatRoughness,
      transmission: this.materialProperties.transmission,
      thickness: this.materialProperties.thickness,
      ior: this.materialProperties.ior,
      reflectivity: this.materialProperties.reflectivity,
      iridescence: this.materialProperties.iridescence,
      iridescenceIOR: this.materialProperties.iridescenceIOR,
      sheen: this.materialProperties.sheen,
      sheenRoughness: this.materialProperties.sheenRoughness,
      sheenColor: this.materialProperties.sheenColor,
      specularIntensity: this.materialProperties.specularIntensity,
      specularColor: this.materialProperties.specularColor,
    });
  }

  createMesh() {
    this.mesh = new THREE.Mesh(this.geometry, this.material);
    this.mesh.position.set(0, 0, 0);
    this.mesh.scale.set(this.scaleProperties.x, this.scaleProperties.y, this.scaleProperties.z);
    this.mesh.receiveShadow = true;
  }

  addToScene() {
    if (this.scene && this.mesh) {
      this.scene.add(this.mesh);
    }
  }

  // 计算旋转动画
  calculateRotationAnimation() {
    if (!this.mesh || !this.animationFlags.rotation) return;

    this.mesh.rotation.x += this.animationSpeed.x;
    this.mesh.rotation.y += this.animationSpeed.y;
    this.mesh.rotation.z += this.animationSpeed.z;
  }

  // 计算脉动效果
  calculatePulseAnimation() {
    if (!this.mesh || !this.animationFlags.pulse) return;

    const pulseFactor = 1 + Math.sin(this.time * 0.5) * 0.002;
    this.mesh.scale.set(this.scaleProperties.x * pulseFactor, this.scaleProperties.y * pulseFactor, this.scaleProperties.z * pulseFactor);
  }

  // 计算透明度动画
  calculateOpacityAnimation() {
    if (!this.material || !this.animationFlags.opacity) return;

    // 基于用户设置的透明度进行动画变化，而不是固定范围
    const baseOpacity = this.materialProperties.opacity;
    const opacityVariation = Math.sin(this.time * 0.3) * 0.1; // 变化幅度为±0.1
    this.material.opacity = Math.max(0.01, Math.min(1.0, baseOpacity + opacityVariation));
  }

  // 更新时间计数器
  updateTimeCounter() {
    this.time += 0.01;
  }

  update() {
    if (this.mesh) {
      // 更新时间计数器
      this.updateTimeCounter();

      // 执行各种动画计算
      this.calculateRotationAnimation(); // 基础旋转动画 - 模拟大气环流
      // this.calculatePulseAnimation(); // 添加微妙的脉动效果，模拟云层厚度变化
      // this.calculateOpacityAnimation(); // 动态调整透明度，模拟云层密度变化
    }
  }

  // 新增方法：设置动画速度
  setAnimationSpeed(speedMultiplier = 1) {
    this.animationSpeed = {
      x: 0.0002 * speedMultiplier,
      y: 0.0005 * speedMultiplier,
      z: 0.0001 * speedMultiplier,
    };
  }

  // 新增方法：暂停/恢复动画
  pauseAnimation() {
    this.animationSpeed = { x: 0, y: 0, z: 0 };
  }

  // 新增方法：重置动画到默认速度
  resumeAnimation() {
    this.animationSpeed = {
      x: 0.0002,
      y: 0.0005,
      z: 0.0001,
    };
  }

  // 新增方法：控制特定动画效果的开关
  toggleRotationAnimation(enabled = true) {
    this.animationFlags.rotation = enabled;
  }

  togglePulseAnimation(enabled = true) {
    this.animationFlags.pulse = enabled;
  }

  toggleOpacityAnimation(enabled = true) {
    this.animationFlags.opacity = enabled;
  }

  // 新增方法：获取当前动画状态
  getAnimationStatus() {
    return {
      rotation: this.animationFlags.rotation,
      pulse: this.animationFlags.pulse,
      opacity: this.animationFlags.opacity,
      speed: this.animationSpeed,
      time: this.time,
    };
  }

  // Initialize GUI controls
  initGUI() {
    // Create GUI if it doesn't exist
    if (!window.cloudsGUI) {
      this.gui = new GUI({
        title: "☁️ 云层控制",
        width: 280,
      });
      window.cloudsGUI = this.gui;
    } else {
      this.gui = window.cloudsGUI;
    }

    // Create clouds folder
    this.folder = this.gui.addFolder("云层设置");
    this.folder.open();

    this.setupGUIControls();
  }

  // Setup GUI controls
  setupGUIControls() {
    // Visibility toggle
    this.folder
      .add(this.materialProperties, "visible")
      .name("显示/隐藏")
      .onChange((value) => {
        this.setVisible(value);
      });

    // Opacity control
    this.folder
      .add(this.materialProperties, "opacity", 0.0, 1.0, 0.01)
      .name("透明度")
      .onChange((value) => {
        this.setOpacity(value);
      });

    // Color control
    this.folder
      .addColor(this.materialProperties, "color")
      .name("颜色")
      .onChange((value) => {
        this.setColor(value);
      });

    // Alpha test control
    this.folder
      .add(this.materialProperties, "alphaTest", 0.0, 1.0, 0.01)
      .name("透明度测试")
      .onChange((value) => {
        this.setAlphaTest(value);
      });

    // Physical Material Properties folder
    const physicalFolder = this.folder.addFolder("物理材质属性");

    // Basic physical properties
    physicalFolder
      .add(this.materialProperties, "metalness", 0.0, 1.0, 0.01)
      .name("金属度")
      .onChange((value) => {
        this.setMetalness(value);
      });

    physicalFolder
      .add(this.materialProperties, "roughness", 0.0, 1.0, 0.01)
      .name("粗糙度")
      .onChange((value) => {
        this.setRoughness(value);
      });

    physicalFolder
      .add(this.materialProperties, "reflectivity", 0.0, 1.0, 0.01)
      .name("反射率")
      .onChange((value) => {
        this.setReflectivity(value);
      });

    physicalFolder
      .add(this.materialProperties, "ior", 1.0, 2.333, 0.01)
      .name("折射率")
      .onChange((value) => {
        this.setIor(value);
      });

    // Clearcoat properties
    const clearcoatFolder = physicalFolder.addFolder("清漆层");

    clearcoatFolder
      .add(this.materialProperties, "clearcoat", 0.0, 1.0, 0.01)
      .name("清漆强度")
      .onChange((value) => {
        this.setClearcoat(value);
      });

    clearcoatFolder
      .add(this.materialProperties, "clearcoatRoughness", 0.0, 1.0, 0.01)
      .name("清漆粗糙度")
      .onChange((value) => {
        this.setClearcoatRoughness(value);
      });

    // Transmission properties
    const transmissionFolder = physicalFolder.addFolder("透射属性");

    transmissionFolder
      .add(this.materialProperties, "transmission", 0.0, 1.0, 0.01)
      .name("透射率")
      .onChange((value) => {
        this.setTransmission(value);
      });

    transmissionFolder
      .add(this.materialProperties, "thickness", 0.0, 5.0, 0.01)
      .name("厚度")
      .onChange((value) => {
        this.setThickness(value);
      });

    // Iridescence properties
    const iridescenceFolder = physicalFolder.addFolder("彩虹效应");

    iridescenceFolder
      .add(this.materialProperties, "iridescence", 0.0, 1.0, 0.01)
      .name("彩虹强度")
      .onChange((value) => {
        this.setIridescence(value);
      });

    iridescenceFolder
      .add(this.materialProperties, "iridescenceIOR", 1.0, 2.333, 0.01)
      .name("彩虹折射率")
      .onChange((value) => {
        this.setIridescenceIOR(value);
      });

    // Sheen properties
    const sheenFolder = physicalFolder.addFolder("光泽效应");

    sheenFolder
      .add(this.materialProperties, "sheen", 0.0, 1.0, 0.01)
      .name("光泽强度")
      .onChange((value) => {
        this.setSheen(value);
      });

    sheenFolder
      .add(this.materialProperties, "sheenRoughness", 0.0, 1.0, 0.01)
      .name("光泽粗糙度")
      .onChange((value) => {
        this.setSheenRoughness(value);
      });

    sheenFolder
      .addColor(this.materialProperties, "sheenColor")
      .name("光泽颜色")
      .onChange((value) => {
        this.setSheenColor(value);
      });

    // Specular properties
    const specularFolder = physicalFolder.addFolder("镜面反射");

    specularFolder
      .add(this.materialProperties, "specularIntensity", 0.0, 1.0, 0.01)
      .name("镜面强度")
      .onChange((value) => {
        this.setSpecularIntensity(value);
      });

    specularFolder
      .addColor(this.materialProperties, "specularColor")
      .name("镜面颜色")
      .onChange((value) => {
        this.setSpecularColor(value);
      });

    // Animation controls folder
    const animationFolder = this.folder.addFolder("动画控制");

    // Rotation animation toggle
    animationFolder
      .add(this.animationFlags, "rotation")
      .name("旋转动画")
      .onChange((value) => {
        this.toggleRotationAnimation(value);
      });

    // Pulse animation toggle
    animationFolder
      .add(this.animationFlags, "pulse")
      .name("脉动效果")
      .onChange((value) => {
        this.togglePulseAnimation(value);
      });

    // Opacity animation toggle
    animationFolder
      .add(this.animationFlags, "opacity")
      .name("透明度动画")
      .onChange((value) => {
        this.toggleOpacityAnimation(value);
        // 如果关闭透明度动画，恢复到用户设置的透明度
        if (!value && this.material) {
          this.material.opacity = this.materialProperties.opacity;
        }
      });

    // Animation speed controls
    const speedFolder = animationFolder.addFolder("旋转速度");

    speedFolder
      .add(this.animationSpeed, "x", -0.001, 0.001, 0.0001)
      .name("X轴速度")
      .onChange((value) => {
        this.animationSpeed.x = value;
      });

    speedFolder
      .add(this.animationSpeed, "y", -0.001, 0.001, 0.0001)
      .name("Y轴速度")
      .onChange((value) => {
        this.animationSpeed.y = value;
      });

    speedFolder
      .add(this.animationSpeed, "z", -0.001, 0.001, 0.0001)
      .name("Z轴速度")
      .onChange((value) => {
        this.animationSpeed.z = value;
      });

    // Scale controls folder
    const scaleFolder = this.folder.addFolder("缩放控制");

    // Uniform scale control
    scaleFolder
      .add(this.scaleProperties, "uniform", 0.5, 2.0, 0.001)
      .name("统一缩放")
      .onChange((value) => {
        this.setUniformScale(value);
      });

    // Individual axis scale controls
    const axisScaleFolder = scaleFolder.addFolder("轴向缩放");

    axisScaleFolder
      .add(this.scaleProperties, "x", 0.5, 2.0, 0.001)
      .name("X轴缩放")
      .onChange((value) => {
        this.setScale("x", value);
        // 更新统一缩放显示（取平均值）
        this.scaleProperties.uniform = (this.scaleProperties.x + this.scaleProperties.y + this.scaleProperties.z) / 3;
      });

    axisScaleFolder
      .add(this.scaleProperties, "y", 0.5, 2.0, 0.001)
      .name("Y轴缩放")
      .onChange((value) => {
        this.setScale("y", value);
        // 更新统一缩放显示（取平均值）
        this.scaleProperties.uniform = (this.scaleProperties.x + this.scaleProperties.y + this.scaleProperties.z) / 3;
      });

    axisScaleFolder
      .add(this.scaleProperties, "z", 0.5, 2.0, 0.001)
      .name("Z轴缩放")
      .onChange((value) => {
        this.setScale("z", value);
        // 更新统一缩放显示（取平均值）
        this.scaleProperties.uniform = (this.scaleProperties.x + this.scaleProperties.y + this.scaleProperties.z) / 3;
      });
  }

  // Method to set visibility
  setVisible(visible) {
    this.materialProperties.visible = visible;
    if (this.mesh) {
      this.mesh.visible = visible;
    }
  }

  // Method to set opacity
  setOpacity(opacity) {
    this.materialProperties.opacity = opacity;
    if (this.material) {
      // 如果透明度动画关闭，直接设置透明度
      if (!this.animationFlags.opacity) {
        this.material.opacity = opacity;
      }
      // 如果透明度动画开启，透明度会在calculateOpacityAnimation中基于这个值进行动画
    }
  }

  // Method to set color
  setColor(color) {
    this.materialProperties.color = color;
    if (this.material) {
      this.material.color.set(color);
    }
  }

  // Method to set alpha test
  setAlphaTest(alphaTest) {
    this.materialProperties.alphaTest = alphaTest;
    if (this.material) {
      this.material.alphaTest = alphaTest;
      this.material.needsUpdate = true;
    }
  }

  // Method to set scale for individual axes
  setScale(axis, value) {
    this.scaleProperties[axis] = value;
    this.updateMeshScale();
  }

  // Method to set uniform scale
  setUniformScale(value) {
    this.scaleProperties.uniform = value;
    this.scaleProperties.x = value;
    this.scaleProperties.y = value;
    this.scaleProperties.z = value;
    this.updateMeshScale();
  }

  // Method to update mesh scale
  updateMeshScale() {
    if (this.mesh && !this.animationFlags.pulse) {
      this.mesh.scale.set(this.scaleProperties.x, this.scaleProperties.y, this.scaleProperties.z);
    }
  }

  // Methods for MeshPhysicalMaterial properties
  setMetalness(value) {
    this.materialProperties.metalness = value;
    if (this.material) {
      this.material.metalness = value;
    }
  }

  setRoughness(value) {
    this.materialProperties.roughness = value;
    if (this.material) {
      this.material.roughness = value;
    }
  }

  setClearcoat(value) {
    this.materialProperties.clearcoat = value;
    if (this.material) {
      this.material.clearcoat = value;
    }
  }

  setClearcoatRoughness(value) {
    this.materialProperties.clearcoatRoughness = value;
    if (this.material) {
      this.material.clearcoatRoughness = value;
    }
  }

  setTransmission(value) {
    this.materialProperties.transmission = value;
    if (this.material) {
      this.material.transmission = value;
    }
  }

  setThickness(value) {
    this.materialProperties.thickness = value;
    if (this.material) {
      this.material.thickness = value;
    }
  }

  setIor(value) {
    this.materialProperties.ior = value;
    if (this.material) {
      this.material.ior = value;
    }
  }

  setReflectivity(value) {
    this.materialProperties.reflectivity = value;
    if (this.material) {
      this.material.reflectivity = value;
    }
  }

  setIridescence(value) {
    this.materialProperties.iridescence = value;
    if (this.material) {
      this.material.iridescence = value;
    }
  }

  setIridescenceIOR(value) {
    this.materialProperties.iridescenceIOR = value;
    if (this.material) {
      this.material.iridescenceIOR = value;
    }
  }

  setSheen(value) {
    this.materialProperties.sheen = value;
    if (this.material) {
      this.material.sheen = value;
    }
  }

  setSheenRoughness(value) {
    this.materialProperties.sheenRoughness = value;
    if (this.material) {
      this.material.sheenRoughness = value;
    }
  }

  setSheenColor(value) {
    this.materialProperties.sheenColor = value;
    if (this.material) {
      this.material.sheenColor.set(value);
    }
  }

  setSpecularIntensity(value) {
    this.materialProperties.specularIntensity = value;
    if (this.material) {
      this.material.specularIntensity = value;
    }
  }

  setSpecularColor(value) {
    this.materialProperties.specularColor = value;
    if (this.material) {
      this.material.specularColor.set(value);
    }
  }

  destroy() {
    // Remove GUI folder
    if (this.folder && this.gui) {
      this.gui.removeFolder(this.folder);
    }

    // Clean up GUI if no other folders exist
    if (this.gui && this.gui.folders.length === 0) {
      this.gui.destroy();
      window.cloudsGUI = null;
    }

    // Remove from scene
    if (this.scene && this.mesh) {
      this.scene.remove(this.mesh);
    }

    // Dispose of geometry
    if (this.geometry) {
      this.geometry.dispose();
    }

    // Dispose of material
    if (this.material) {
      this.material.dispose();
    }

    // Dispose of texture
    if (this.texture) {
      this.texture.dispose();
    }

    // Clear references
    this.mesh = null;
    this.geometry = null;
    this.material = null;
    this.texture = null;
    this.gui = null;
    this.folder = null;
  }
}

// Export both named and default exports for flexibility
export { Clouds };
export default Clouds;
