import * as THREE from "three";
import { EARTH_RADIUS } from "../../../../constants/index.js";
import CustomShaderMaterial from "three-custom-shader-material/vanilla";

/**
 * 地球轨道星星系统
 *
 * 在地球表面周围创建环绕运动的星星点点效果，营造梦幻的太空氛围。
 *
 * 特性：
 * 1. 多层轨道的星星分布
 * 2. 不同速度的环绕运动
 * 3. 动态大小和透明度变化
 * 4. 可配置的轨道参数
 * 5. 与地球同步的光照效果
 *
 * @class OrbitingStars
 */
class OrbitingStars {
  constructor(scene, options = {}) {
    this.scene = scene;

    // 配置参数
    this.config = {
      starCount: options.starCount || 250, // 总星星数量 (减少)
      orbitLayers: options.orbitLayers || 5, // 轨道层数 (减少)
      minRadius: options.minRadius || EARTH_RADIUS * 1.03, // 最小轨道半径 (更贴近地球)
      maxRadius: options.maxRadius || EARTH_RADIUS * 1.1, // 最大轨道半径 (更贴近地球)
      minSize: options.minSize || 0.1, // 最小星星大小 (大幅增大以确保可见)
      maxSize: options.maxSize || 0.35, // 最大星星大小 (大幅增大以确保可见)
      minOpacity: options.minOpacity || 0.0, // 最小透明度 (提高基础亮度)
      maxOpacity: options.maxOpacity || 0.3, // 最大透明度 (保持)
      rotationSpeed: options.rotationSpeed || 0.001, // 基础旋转速度 (加快)
      twinkleSpeed: options.twinkleSpeed || 0.8, // 闪烁速度 (加快)
      color: options.color || 0x00b7ff, // 星星颜色 (天蓝色)
    };

    // Three.js 对象
    this.starGroups = [];
    this.geometries = [];
    this.materials = [];

    // 动画相关
    this.time = 0;
    this.rotationSpeeds = [];
    this.originalOpacities = [];
    this.originalSizes = [];
    this.twinkleOffsets = [];
    this.sizeOffsets = [];
    this.brightnessOffsets = [];
    this.sizeAnimationTypes = []; // 新增：每个星星的动画类型
    this.sizeFrequencies = []; // 新增：每个星星的频率
    this.sizeAmplitudes = []; // 新增：每个星星的振幅
    this.radialOffsets = []; // 新增：径向动画偏移
    this.radialFrequencies = []; // 新增：径向动画频率
    this.radialAmplitudes = []; // 新增：径向动画振幅
    this.originalPositions = []; // 新增：保存原始位置

    this.init();
  }

  init() {
    console.log("OrbitingStars: 开始初始化");
    this.createOrbitingStars();
    this.addToScene();
    console.log(`OrbitingStars: 初始化完成，创建了 ${this.starGroups.length} 个星星层`);
  }

  /**
   * 创建多层轨道的星星
   */
  createOrbitingStars() {
    const starsPerLayer = Math.floor(this.config.starCount / this.config.orbitLayers);

    for (let layer = 0; layer < this.config.orbitLayers; layer++) {
      this.createStarLayer(layer, starsPerLayer);
    }
  }

  /**
   * 创建单个轨道层的星星
   */
  createStarLayer(layerIndex, starCount) {
    console.log(`OrbitingStars: 创建第 ${layerIndex + 1} 层星星，数量: ${starCount}`);

    // 计算当前层的轨道半径
    const radiusProgress = layerIndex / (this.config.orbitLayers - 1);
    const orbitRadius = this.config.minRadius + (this.config.maxRadius - this.config.minRadius) * radiusProgress;

    console.log(`OrbitingStars: 第 ${layerIndex + 1} 层轨道半径: ${orbitRadius.toFixed(2)}`);

    // 计算旋转速度（内层更快）
    const speedMultiplier = 1 + (this.config.orbitLayers - layerIndex - 1) * 0.5;
    this.rotationSpeeds[layerIndex] = this.config.rotationSpeed * speedMultiplier;

    // 创建基础几何体用于星星 - 使用平面几何体确保UV坐标正确
    const starGeometry = new THREE.PlaneGeometry(1.0, 1.0); // 使用平面几何体，UV坐标从(0,0)到(1,1)

    // 创建实例化属性
    const instancePositions = new Float32Array(starCount * 3);
    const instanceSizes = new Float32Array(starCount);
    const instanceOpacities = new Float32Array(starCount);
    const layerOpacities = [];
    const layerSizes = [];
    const layerTwinkleOffsets = [];
    const layerSizeOffsets = [];
    const layerBrightnessOffsets = [];
    const layerAnimationTypes = []; // 新增：动画类型数组
    const layerSizeFrequencies = []; // 新增：频率数组
    const layerSizeAmplitudes = []; // 新增：振幅数组
    const layerRadialOffsets = []; // 新增：径向动画偏移数组
    const layerRadialFrequencies = []; // 新增：径向动画频率数组
    const layerRadialAmplitudes = []; // 新增：径向动画振幅数组
    const layerOriginalPositions = []; // 新增：原始位置数组

    // 生成星星位置和属性
    for (let i = 0; i < starCount; i++) {
      const i3 = i * 3;

      // 在球面上更均匀分布，减少聚集
      const theta = (i / starCount) * Math.PI * 2 + Math.random() * 0.2; // 方位角，减少随机性
      const phi = Math.acos(2 * Math.random() - 1); // 极角

      // 减少半径变化，让星星更贴近轨道
      const radiusVariation = orbitRadius + (Math.random() - 0.5) * EARTH_RADIUS * 0.05;

      // 转换为笛卡尔坐标
      const x = radiusVariation * Math.sin(phi) * Math.cos(theta);
      const y = radiusVariation * Math.sin(phi) * Math.sin(theta);
      const z = radiusVariation * Math.cos(phi);

      instancePositions[i3] = x;
      instancePositions[i3 + 1] = y;
      instancePositions[i3 + 2] = z;

      // 保存原始位置（用于径向动画）
      layerOriginalPositions[i] = { x, y, z, radius: radiusVariation };

      // 随机大小（外层星星稍大）
      const sizeMultiplier = 0.8 + radiusProgress * 0.4;
      const size = (this.config.minSize + Math.random() * (this.config.maxSize - this.config.minSize)) * sizeMultiplier;
      instanceSizes[i] = size;

      // 随机透明度
      const opacity = this.config.minOpacity + Math.random() * (this.config.maxOpacity - this.config.minOpacity);
      instanceOpacities[i] = opacity;

      // 保存原始值和动画偏移
      layerOpacities[i] = opacity;
      layerSizes[i] = size;
      layerTwinkleOffsets[i] = Math.random() * Math.PI * 2;
      layerSizeOffsets[i] = Math.random() * Math.PI * 2;
      layerBrightnessOffsets[i] = Math.random() * Math.PI * 2;

      // 为每个星星分配随机的动画特性
      const animationType = Math.floor(Math.random() * 4); // 0-3 四种动画类型
      layerAnimationTypes[i] = animationType;

      // 随机频率：0.5x 到 3x 的基础频率
      layerSizeFrequencies[i] = 0.5 + Math.random() * 2.5;

      // 随机振幅：0.2 到 0.8 的变化幅度
      layerSizeAmplitudes[i] = 0.2 + Math.random() * 0.6;

      // 径向动画属性
      layerRadialOffsets[i] = Math.random() * Math.PI * 2; // 径向动画相位偏移
      layerRadialFrequencies[i] = 0.3 + Math.random() * 1.2; // 径向动画频率 (0.3x 到 1.5x)
      layerRadialAmplitudes[i] = EARTH_RADIUS * (0.02 + Math.random() * 0.08); // 径向变化幅度 (2%-10%地球半径)
    }

    this.originalOpacities[layerIndex] = layerOpacities;
    this.originalSizes[layerIndex] = layerSizes;
    this.twinkleOffsets[layerIndex] = layerTwinkleOffsets;
    this.sizeOffsets[layerIndex] = layerSizeOffsets;
    this.brightnessOffsets[layerIndex] = layerBrightnessOffsets;
    this.sizeAnimationTypes[layerIndex] = layerAnimationTypes;
    this.sizeFrequencies[layerIndex] = layerSizeFrequencies;
    this.sizeAmplitudes[layerIndex] = layerSizeAmplitudes;
    this.radialOffsets[layerIndex] = layerRadialOffsets;
    this.radialFrequencies[layerIndex] = layerRadialFrequencies;
    this.radialAmplitudes[layerIndex] = layerRadialAmplitudes;
    this.originalPositions[layerIndex] = layerOriginalPositions;

    // 创建材质
    const material = this.createStarMaterial();

    // 创建实例化网格
    const instancedMesh = new THREE.InstancedMesh(starGeometry, material, starCount);
    instancedMesh.name = `OrbitingStars_Layer_${layerIndex}`;

    // 确保星星不会因为自身旋转而变形
    instancedMesh.frustumCulled = false; // 禁用视锥体剔除，确保星星始终渲染

    // 设置每个实例的变换矩阵（只包含位置和缩放，不包含旋转）
    const matrix = new THREE.Matrix4();
    for (let i = 0; i < starCount; i++) {
      const position = new THREE.Vector3(instancePositions[i * 3], instancePositions[i * 3 + 1], instancePositions[i * 3 + 2]);
      const scale = instanceSizes[i];

      // 只设置缩放和位置，不设置旋转，确保星星始终面向同一方向
      matrix.makeScale(scale, scale, scale);
      matrix.setPosition(position);

      instancedMesh.setMatrixAt(i, matrix);
    }
    instancedMesh.instanceMatrix.needsUpdate = true;

    // 创建几何体用于存储属性（用于动画更新）
    const attributeGeometry = new THREE.BufferGeometry();
    attributeGeometry.setAttribute("instancePosition", new THREE.BufferAttribute(instancePositions, 3));
    attributeGeometry.setAttribute("size", new THREE.BufferAttribute(instanceSizes, 1));
    attributeGeometry.setAttribute("opacity", new THREE.BufferAttribute(instanceOpacities, 1));

    // 创建旋转组（只用于轨道旋转，不影响星星本身的方向）
    const rotationGroup = new THREE.Group();

    // 确保星星网格保持初始方向
    instancedMesh.rotation.set(0, 0, 0);
    instancedMesh.scale.set(1, 1, 1);

    rotationGroup.add(instancedMesh);

    this.starGroups.push(rotationGroup);
    this.geometries.push(attributeGeometry);
    this.materials.push(material);
    this.instancedMeshes = this.instancedMeshes || [];
    this.instancedMeshes.push(instancedMesh);
  }

  /**
   * 创建星星材质
   */
  createStarMaterial() {
    // 不再需要纹理，直接在着色器中绘制圆形星星

    // 自定义着色器代码
    const vertexShader = `
      varying float vOpacity;
      varying vec2 vUv;

      void main() {
        vOpacity = 1.0; // 固定透明度，后续通过材质控制
        vUv = uv; // 传递UV坐标到片元着色器

        // 使用实例化矩阵变换位置
        vec4 instancePosition = instanceMatrix * vec4(position, 1.0);

        gl_Position = projectionMatrix * modelViewMatrix * instancePosition;
      }
    `;

    const fragmentShader = `
      uniform vec3 starColor;
      uniform float uMetalness;
      uniform float uRoughness;
      uniform float uEmissiveIntensity;
      varying float vOpacity;
      varying vec2 vUv;

      void main() {
        // 将UV坐标转换为以中心为原点的坐标系 (-0.5 到 0.5)
        vec2 center = vUv - 0.5;

        // 计算到中心的距离
        float distance = length(center);

        // 创建完美的圆形，半径为0.5
        float circle = 1.0 - step(0.5, distance);

        // 如果在圆形外部，丢弃片元
        if (circle < 0.5) {
          discard;
        }

        // 简单的径向渐变，中心最亮
        float radialGradient = 1.0 - smoothstep(0.0, 0.5, distance);

        // 使用固定的星星颜色
        vec3 finalColor = starColor;

        // 应用径向渐变
        finalColor *= radialGradient;
        float finalAlpha = vOpacity * circle * radialGradient;

        // 设置材质属性用于PBR渲染
        csm_DiffuseColor = vec4(finalColor, finalAlpha);
        csm_Emissive = finalColor * uEmissiveIntensity;
        csm_Metalness = uMetalness;
        csm_Roughness = uRoughness;
      }
    `;

    // 使用 CustomShaderMaterial 结合 MeshStandardMaterial
    return new CustomShaderMaterial({
      baseMaterial: THREE.MeshStandardMaterial,
      vertexShader,
      fragmentShader,
      uniforms: {
        starColor: { value: new THREE.Color(this.config.color) },
        uMetalness: { value: 0.0 },
        uRoughness: { value: 1.0 },
        uEmissiveIntensity: { value: 1.0 },
      },
      // MeshStandardMaterial 属性
      color: new THREE.Color(this.config.color),
      emissive: new THREE.Color(this.config.color),
      emissiveIntensity: 0.5,
      metalness: 0.0,
      roughness: 1.0,
      transparent: true,
      alphaTest: 0.01, // 启用 alpha 测试以正确处理透明度
      depthWrite: false,
      side: THREE.DoubleSide, // 确保双面可见，对平面几何体很重要
    });
  }

  /**
   * 添加到场景
   */
  addToScene() {
    this.starGroups.forEach((group) => {
      if (this.scene) {
        this.scene.add(group);
      }
    });
  }

  /**
   * 更新动画
   */
  update() {
    this.time += 0.016; // 约60fps

    // 更新每个轨道层
    this.starGroups.forEach((group, layerIndex) => {
      // 只进行轨道旋转，星星本身保持固定方向
      group.rotation.y += this.rotationSpeeds[layerIndex];

      // 更新闪烁效果
      this.updateTwinkle(layerIndex);

      // 更新大小变化
      this.updateSizeAnimation(layerIndex);

      // 更新径向位置变化
      this.updateRadialAnimation(layerIndex);
    });
  }

  /**
   * 更新闪烁效果
   */
  updateTwinkle(layerIndex) {
    const geometry = this.geometries[layerIndex];
    const instancedMesh = this.instancedMeshes[layerIndex];
    if (!geometry || !geometry.attributes.opacity || !instancedMesh) return;

    const opacities = geometry.attributes.opacity.array;
    const originalOpacities = this.originalOpacities[layerIndex];
    const twinkleOffsets = this.twinkleOffsets[layerIndex];

    for (let i = 0; i < opacities.length; i++) {
      // 增强闪烁效果：使用多重正弦波叠加
      const primaryTwinkle = Math.sin(this.time * this.config.twinkleSpeed + twinkleOffsets[i]);
      const secondaryTwinkle = Math.sin(this.time * this.config.twinkleSpeed * 1.7 + twinkleOffsets[i] * 2);
      const tertiaryTwinkle = Math.sin(this.time * this.config.twinkleSpeed * 0.3 + twinkleOffsets[i] * 3);

      // 组合闪烁效果，创造更复杂的亮度变化
      const combinedTwinkle = (primaryTwinkle * 0.5 + secondaryTwinkle * 0.3 + tertiaryTwinkle * 0.2) * 0.5 + 0.5;

      // 添加脉冲效果
      const pulseEffect = Math.sin(this.time * this.config.twinkleSpeed * 0.1 + twinkleOffsets[i]) * 0.2 + 0.8;

      // 添加随机闪烁强度
      const intensity = 0.1 + Math.sin(twinkleOffsets[i] + this.time * 0.002) * 0.4;

      // 最终透明度计算 - 提高基础亮度
      const finalOpacity = originalOpacities[i] * combinedTwinkle * pulseEffect * (0.6 + intensity * 0.4);
      opacities[i] = Math.max(0.3, Math.min(1.0, finalOpacity)); // 提高最小透明度确保更亮
    }

    geometry.attributes.opacity.needsUpdate = true;
  }

  /**
   * 更新大小动画效果 - 增强随机性和不规律性
   */
  updateSizeAnimation(layerIndex) {
    const geometry = this.geometries[layerIndex];
    const instancedMesh = this.instancedMeshes[layerIndex];
    if (!geometry || !geometry.attributes.size || !instancedMesh) return;

    const sizes = geometry.attributes.size.array;
    const originalSizes = this.originalSizes[layerIndex];
    const sizeOffsets = this.sizeOffsets[layerIndex];
    const brightnessOffsets = this.brightnessOffsets[layerIndex];
    const animationTypes = this.sizeAnimationTypes[layerIndex];
    const frequencies = this.sizeFrequencies[layerIndex];
    const amplitudes = this.sizeAmplitudes[layerIndex];
    // 注意：实例矩阵更新现在在updateRadialAnimation中统一处理

    for (let i = 0; i < sizes.length; i++) {
      let sizeMultiplier = 1.0;

      // 根据动画类型使用不同的算法
      switch (animationTypes[i]) {
        case 0: // 标准正弦波
          sizeMultiplier = 0.7 + Math.sin(this.time * this.config.twinkleSpeed * frequencies[i] + sizeOffsets[i]) * amplitudes[i];
          break;

        case 1: // 复合波形（正弦 + 余弦）
          const wave1 = Math.sin(this.time * this.config.twinkleSpeed * frequencies[i] + sizeOffsets[i]);
          const wave2 = Math.cos(this.time * this.config.twinkleSpeed * frequencies[i] * 1.3 + brightnessOffsets[i]);
          sizeMultiplier = 0.7 + (wave1 * 0.6 + wave2 * 0.4) * amplitudes[i];
          break;

        case 2: // 脉冲式变化
          const pulse = Math.abs(Math.sin(this.time * this.config.twinkleSpeed * frequencies[i] * 0.5 + sizeOffsets[i]));
          const smoothPulse = Math.pow(pulse, 2); // 平方使变化更突然
          sizeMultiplier = 0.5 + smoothPulse * amplitudes[i] * 1.2;
          break;

        case 3: // 随机抖动
          const baseWave = Math.sin(this.time * this.config.twinkleSpeed * frequencies[i] + sizeOffsets[i]);
          const noise = Math.sin(this.time * 13.7 + sizeOffsets[i] * 7.3) * Math.cos(this.time * 17.1 + brightnessOffsets[i] * 5.7) * 0.3;
          sizeMultiplier = 0.7 + (baseWave + noise) * amplitudes[i];
          break;
      }

      // 添加全局的微小随机抖动
      const globalNoise = Math.sin(this.time * 0.1 + sizeOffsets[i] * 100) * 0.05;
      sizeMultiplier += globalNoise;

      // 确保大小在合理范围内
      sizeMultiplier = Math.max(0.3, Math.min(1.5, sizeMultiplier));

      const finalSize = originalSizes[i] * sizeMultiplier;
      sizes[i] = finalSize;

      // 注意：位置更新现在在updateRadialAnimation中处理，这里只更新大小
    }

    geometry.attributes.size.needsUpdate = true;
    // 注意：instanceMatrix更新现在在updateRadialAnimation中处理
  }

  /**
   * 更新径向位置动画效果 - 添加深度变化
   */
  updateRadialAnimation(layerIndex) {
    const geometry = this.geometries[layerIndex];
    const instancedMesh = this.instancedMeshes[layerIndex];
    if (!geometry || !geometry.attributes.instancePosition || !instancedMesh) return;

    const positions = geometry.attributes.instancePosition.array;
    const originalPositions = this.originalPositions[layerIndex];
    const radialOffsets = this.radialOffsets[layerIndex];
    const radialFrequencies = this.radialFrequencies[layerIndex];
    const radialAmplitudes = this.radialAmplitudes[layerIndex];
    const sizes = geometry.attributes.size.array;

    // 更新实例矩阵以反映位置和大小变化
    const matrix = new THREE.Matrix4();

    for (let i = 0; i < originalPositions.length; i++) {
      const originalPos = originalPositions[i];
      const i3 = i * 3;

      // 计算径向动画偏移
      const radialWave = Math.sin(this.time * this.config.twinkleSpeed * radialFrequencies[i] + radialOffsets[i]);
      const radialOffset = radialWave * radialAmplitudes[i];

      // 计算新的半径
      const newRadius = originalPos.radius + radialOffset;

      // 保持原始的角度方向，只改变距离
      const radiusRatio = newRadius / originalPos.radius;

      // 更新位置
      const newX = originalPos.x * radiusRatio;
      const newY = originalPos.y * radiusRatio;
      const newZ = originalPos.z * radiusRatio;

      positions[i3] = newX;
      positions[i3 + 1] = newY;
      positions[i3 + 2] = newZ;

      // 更新实例矩阵（包含新位置和当前大小）
      const position = new THREE.Vector3(newX, newY, newZ);
      const scale = sizes[i];

      matrix.makeScale(scale, scale, scale);
      matrix.setPosition(position);

      instancedMesh.setMatrixAt(i, matrix);
    }

    geometry.attributes.instancePosition.needsUpdate = true;
    instancedMesh.instanceMatrix.needsUpdate = true;
  }

  /**
   * 设置可见性
   */
  setVisible(visible) {
    this.starGroups.forEach((group) => {
      group.visible = visible;
    });
  }

  /**
   * 设置颜色
   */
  setColor(color) {
    this.config.color = color;
    this.materials.forEach((material) => {
      if (material.uniforms && material.uniforms.starColor) {
        material.uniforms.starColor.value.set(color);
      }
    });
  }

  /**
   * 销毁资源
   */
  destroy() {
    // 从场景中移除
    this.starGroups.forEach((group) => {
      if (this.scene) {
        this.scene.remove(group);
      }
    });

    // 清理几何体
    this.geometries.forEach((geometry) => {
      geometry.dispose();
    });

    // 清理材质
    this.materials.forEach((material) => {
      material.dispose();
    });

    // 清理实例化网格
    if (this.instancedMeshes) {
      this.instancedMeshes.forEach((mesh) => {
        if (mesh.geometry) mesh.geometry.dispose();
        if (mesh.material) mesh.material.dispose();
      });
    }

    // 清理数组
    this.starGroups = [];
    this.geometries = [];
    this.materials = [];
    this.instancedMeshes = [];
    this.rotationSpeeds = [];
    this.originalOpacities = [];
    this.originalSizes = [];
    this.twinkleOffsets = [];
    this.sizeOffsets = [];
    this.brightnessOffsets = [];
    this.sizeAnimationTypes = [];
    this.sizeFrequencies = [];
    this.sizeAmplitudes = [];
    this.radialOffsets = [];
    this.radialFrequencies = [];
    this.radialAmplitudes = [];
    this.originalPositions = [];
  }
}

export { OrbitingStars };
